#ifndef HOMEWIDGET_H
#define HOMEWIDGET_H

#include <QWidget>
#include "admc_api_wrapper.h"
#include "statuswidget.h"
#include "unitconverter.h"

namespace Ui {
class HomeWidget;
}

class HomeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit HomeWidget(QWidget *parent = nullptr);
    ~HomeWidget();

private slots:
    void startHome();
    void updateStatus(const QString &message);
    void onApiStatusChanged(const QString& message, bool isSuccess);
    void onAxisChanged(int axis);
    void onUnitTypeChanged(UnitType type);
    void onUnitSwitchClicked();

private:
    Ui::HomeWidget *ui;
    bool m_isHoming[4]; // 每个轴的回零状态
    AdmcApiWrapper* m_apiWrapper;
    StatusWidget* m_statusWidget; // 轴状态模块引用
    QTimer* m_statusTimer[4]; // 每个轴的状态检测定时器
    UnitConverter* m_unitConverter; // 单位转换器

    // 更新界面单位显示
    void updateUnitDisplay();
    // 设置默认参数值
    void setDefaultParameters();

    // 检查回零状态
    void checkHomeStatus(int axis);

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);
};

#endif // HOMEWIDGET_H