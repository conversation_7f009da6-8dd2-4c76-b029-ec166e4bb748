#include "homewidget.h"
#include "ui_homewidget.h"
#include <QMessageBox>
#include <QTabWidget>
#include <QTimer>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QSpinBox>

HomeWidget::HomeWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::HomeWidget),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_statusWidget(nullptr),
    m_unitConverter(UnitConverter::getInstance())
{
    // 初始化回零状态和定时器
    for (int i = 0; i < 4; ++i) {
        m_isHoming[i] = false;
        m_statusTimer[i] = new QTimer(this);
        connect(m_statusTimer[i], &QTimer::timeout, [this, i]() {
            checkHomeStatus(i);
        });
        m_statusTimer[i]->setInterval(500); // 每500毫秒检查一次状态
    }

    ui->setupUi(this);

    // 初始化控件
    for (int i = 0; i < 4; ++i) {
        ui->comboAxisSelect->addItem(QString("轴 %1").arg(i));
    }

    // 连接信号槽
    connect(ui->btnStartHome, &QPushButton::clicked, this, &HomeWidget::startHome);
    connect(ui->comboAxisSelect, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &HomeWidget::onAxisChanged);

    // 创建轴参数初始化模块
    createAxisInitWidget();

    // 状态定时器已在构造函数初始化部分初始化

    // 获取轴状态模块实例
    m_statusWidget = StatusWidget::getInstance();

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &HomeWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout->addWidget(unitGroupBox);

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &HomeWidget::onUnitTypeChanged);
    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();

    // 初始化状态
    updateStatus("就绪");
}

HomeWidget::~HomeWidget()
{
    // 停止所有定时器
    for (int i = 0; i < 4; ++i) {
        if (m_statusTimer[i]->isActive()) {
            m_statusTimer[i]->stop();
        }
    }

    delete ui;
}

void HomeWidget::startHome()
{
    int axis = ui->comboAxisSelect->currentIndex();

    // 检查是否已连接
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法执行回零操作");
        emit apiStatusChanged("设备未连接，无法执行回零操作", false);
        return;
    }

    // 调用底层API执行回零操作
    short result = m_apiWrapper->axisGoHome(axis);

    if (result != 0) {
        QString errorMsg = QString("轴%1回零操作失败，错误码: %2").arg(axis).arg(result);
        QMessageBox::warning(this, "错误", errorMsg);
        emit apiStatusChanged(errorMsg, false);
        return;
    }

    QString statusMsg = QString("轴%1正在执行回零操作").arg(axis);
    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);

    // 只更新当前轴的回零状态
    m_isHoming[axis] = true;

    // 启动当前轴的状态检测定时器
    m_statusTimer[axis]->start();
}





void HomeWidget::updateStatus(const QString &message)
{
    ui->labelStatus->setText(message);
}

void HomeWidget::onApiStatusChanged(const QString& message, bool isSuccess)
{
    // 更新状态标签
    updateStatus(message);

    // 将信号转发给MainWindow
    emit apiStatusChanged(message, isSuccess);
}

void HomeWidget::onAxisChanged(int axis)
{
    // 当轴变化时，更新状态显示
    QString statusMsg;

    if (m_isHoming[axis]) {
        // 如果切换到的轴正在回零，显示其状态
        statusMsg = QString("轴%1正在执行回零操作").arg(axis);
    } else {
        // 如果切换到的轴没有回零，显示其就绪状态
        statusMsg = QString("轴%1就绪，可以开始回零操作").arg(axis);
    }

    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);
}

void HomeWidget::updateUnitDisplay()
{
    // 单位显示更新逻辑（如果需要的话）
}

void HomeWidget::setDefaultParameters()
{
    // 设置默认参数逻辑（如果需要的话）
}

void HomeWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();
}

void HomeWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}

void HomeWidget::createAxisInitWidget()
{
    // 创建轴参数初始化分组框
    QGroupBox* axisInitGroupBox = new QGroupBox("轴参数初始化");
    QVBoxLayout* axisInitLayout = new QVBoxLayout(axisInitGroupBox);

    // 创建坐标系选择
    QHBoxLayout* crdLayout = new QHBoxLayout();
    crdLayout->addWidget(new QLabel("坐标系:"));
    QComboBox* crdComboBox = new QComboBox();
    crdComboBox->addItem("坐标系 0");
    crdComboBox->addItem("坐标系 1");
    crdLayout->addWidget(crdComboBox);
    crdLayout->addStretch();

    // 创建轴映射设置
    QHBoxLayout* axisMapLayout = new QHBoxLayout();
    axisMapLayout->addWidget(new QLabel("轴映射:"));
    axisMapLayout->addWidget(new QLabel("X轴:"));
    QSpinBox* xAxisSpinBox = new QSpinBox();
    xAxisSpinBox->setRange(0, 3);
    xAxisSpinBox->setValue(0);
    axisMapLayout->addWidget(xAxisSpinBox);

    axisMapLayout->addWidget(new QLabel("Y轴:"));
    QSpinBox* yAxisSpinBox = new QSpinBox();
    yAxisSpinBox->setRange(0, 3);
    yAxisSpinBox->setValue(1);
    axisMapLayout->addWidget(yAxisSpinBox);
    axisMapLayout->addStretch();

    // 创建轴方向设置
    QHBoxLayout* axisDirLayout = new QHBoxLayout();
    axisDirLayout->addWidget(new QLabel("轴方向:"));
    axisDirLayout->addWidget(new QLabel("X方向:"));
    QComboBox* xDirComboBox = new QComboBox();
    xDirComboBox->addItem("0");
    xDirComboBox->addItem("1");
    xDirComboBox->setCurrentIndex(1);
    axisDirLayout->addWidget(xDirComboBox);

    axisDirLayout->addWidget(new QLabel("Y方向:"));
    QComboBox* yDirComboBox = new QComboBox();
    yDirComboBox->addItem("0");
    yDirComboBox->addItem("1");
    yDirComboBox->setCurrentIndex(1);
    axisDirLayout->addWidget(yDirComboBox);
    axisDirLayout->addStretch();

    // 创建最大速度设置
    QHBoxLayout* velMaxLayout = new QHBoxLayout();
    velMaxLayout->addWidget(new QLabel("最大速度:"));
    velMaxLayout->addWidget(new QLabel("X轴:"));
    QSpinBox* xVelMaxSpinBox = new QSpinBox();
    xVelMaxSpinBox->setRange(1, 100000);
    xVelMaxSpinBox->setValue(3000);
    xVelMaxSpinBox->setSuffix(" pulse/ms");
    velMaxLayout->addWidget(xVelMaxSpinBox);

    velMaxLayout->addWidget(new QLabel("Y轴:"));
    QSpinBox* yVelMaxSpinBox = new QSpinBox();
    yVelMaxSpinBox->setRange(1, 100000);
    yVelMaxSpinBox->setValue(3000);
    yVelMaxSpinBox->setSuffix(" pulse/ms");
    velMaxLayout->addWidget(yVelMaxSpinBox);
    velMaxLayout->addStretch();

    // 创建最大加速度设置
    QHBoxLayout* accMaxLayout = new QHBoxLayout();
    accMaxLayout->addWidget(new QLabel("最大加速度:"));
    accMaxLayout->addWidget(new QLabel("X轴:"));
    QSpinBox* xAccMaxSpinBox = new QSpinBox();
    xAccMaxSpinBox->setRange(1, 100000);
    xAccMaxSpinBox->setValue(30);
    xAccMaxSpinBox->setSuffix(" pulse/ms²");
    accMaxLayout->addWidget(xAccMaxSpinBox);

    accMaxLayout->addWidget(new QLabel("Y轴:"));
    QSpinBox* yAccMaxSpinBox = new QSpinBox();
    yAccMaxSpinBox->setRange(1, 100000);
    yAccMaxSpinBox->setValue(30);
    yAccMaxSpinBox->setSuffix(" pulse/ms²");
    accMaxLayout->addWidget(yAccMaxSpinBox);
    accMaxLayout->addStretch();

    // 创建正向限位设置
    QHBoxLayout* positiveLimitLayout = new QHBoxLayout();
    positiveLimitLayout->addWidget(new QLabel("正向限位:"));
    positiveLimitLayout->addWidget(new QLabel("X轴:"));
    QSpinBox* xPositiveSpinBox = new QSpinBox();
    xPositiveSpinBox->setRange(-1000000, 1000000);
    xPositiveSpinBox->setValue(100000);
    xPositiveSpinBox->setSuffix(" pulse");
    positiveLimitLayout->addWidget(xPositiveSpinBox);

    positiveLimitLayout->addWidget(new QLabel("Y轴:"));
    QSpinBox* yPositiveSpinBox = new QSpinBox();
    yPositiveSpinBox->setRange(-1000000, 1000000);
    yPositiveSpinBox->setValue(100000);
    yPositiveSpinBox->setSuffix(" pulse");
    positiveLimitLayout->addWidget(yPositiveSpinBox);
    positiveLimitLayout->addStretch();

    // 创建负向限位设置
    QHBoxLayout* negativeLimitLayout = new QHBoxLayout();
    negativeLimitLayout->addWidget(new QLabel("负向限位:"));
    negativeLimitLayout->addWidget(new QLabel("X轴:"));
    QSpinBox* xNegativeSpinBox = new QSpinBox();
    xNegativeSpinBox->setRange(-1000000, 1000000);
    xNegativeSpinBox->setValue(0);
    xNegativeSpinBox->setSuffix(" pulse");
    negativeLimitLayout->addWidget(xNegativeSpinBox);

    negativeLimitLayout->addWidget(new QLabel("Y轴:"));
    QSpinBox* yNegativeSpinBox = new QSpinBox();
    yNegativeSpinBox->setRange(-1000000, 1000000);
    yNegativeSpinBox->setValue(0);
    yNegativeSpinBox->setSuffix(" pulse");
    negativeLimitLayout->addWidget(yNegativeSpinBox);
    negativeLimitLayout->addStretch();

    // 创建设置参数按钮
    QPushButton* setParamsButton = new QPushButton("设置参数");
    connect(setParamsButton, &QPushButton::clicked, [=]() {
        // 获取参数值
        short crd = crdComboBox->currentIndex();
        short axisMap[2] = {(short)xAxisSpinBox->value(), (short)yAxisSpinBox->value()};
        short axisDir[2] = {(short)xDirComboBox->currentIndex(), (short)yDirComboBox->currentIndex()};
        int32_t velMax[2] = {xVelMaxSpinBox->value(), yVelMaxSpinBox->value()};
        int32_t accMax[2] = {xAccMaxSpinBox->value(), yAccMaxSpinBox->value()};
        int32_t positive[2] = {xPositiveSpinBox->value(), yPositiveSpinBox->value()};
        int32_t negative[2] = {xNegativeSpinBox->value(), yNegativeSpinBox->value()};

        // 调用API设置轴参数
        short result = m_apiWrapper->setAxisPrm(crd, axisMap, axisDir, velMax, accMax, positive, negative);

        if (result == 0) {
            QString msg = QString("坐标系%1轴参数设置成功").arg(crd);
            updateStatus(msg);
            emit apiStatusChanged(msg, true);
        } else {
            QString msg = QString("坐标系%1轴参数设置失败，错误码: %2").arg(crd).arg(result);
            updateStatus(msg);
            emit apiStatusChanged(msg, false);
        }
    });

    // 将所有控件添加到布局
    axisInitLayout->addLayout(crdLayout);
    axisInitLayout->addLayout(axisMapLayout);
    axisInitLayout->addLayout(axisDirLayout);
    axisInitLayout->addLayout(velMaxLayout);
    axisInitLayout->addLayout(accMaxLayout);
    axisInitLayout->addLayout(positiveLimitLayout);
    axisInitLayout->addLayout(negativeLimitLayout);
    axisInitLayout->addWidget(setParamsButton);

    // 将轴参数初始化模块添加到主布局
    ui->verticalLayout->insertWidget(2, axisInitGroupBox); // 插入到回零操作模块之后，状态模块之前
}

void HomeWidget::checkHomeStatus(int axis)
{
    // 只有在该轴回零过程中才检查状态
    if (!m_isHoming[axis] || !m_apiWrapper->isConnected()) {
        return;
    }

    // 从轴状态模块获取轴状态
    bool isAtOrigin = false;
    bool isResetting = false;

    if (m_statusWidget) {
        // 使用轴状态模块的方法获取状态
        isAtOrigin = m_statusWidget->isAxisAtOrigin(axis);
        isResetting = m_statusWidget->isAxisResetting(axis);
    } else {
        // 如果轴状态模块不可用，则直接获取
        short status = 0;
        short result = m_apiWrapper->getAxisStatus(axis, status);
        if (result == 0) {
            isAtOrigin = (status & (1 << 3)) != 0; // 原点信号
            isResetting = (status & (1 << 8)) != 0; // 复位中
        }
    }

    // 当到达原点且复位结束时，表示回零完成
    if (isAtOrigin && !isResetting) {
        QString statusMsg = QString("轴%1回零操作完成").arg(axis);
        updateStatus(statusMsg);
        emit apiStatusChanged(statusMsg, true);

        // 重置该轴的回零状态
        m_isHoming[axis] = false;

        // 停止该轴的定时器
        m_statusTimer[axis]->stop();

        // 如果当前显示的是该轴，更新UI状态
        int currentAxis = ui->comboAxisSelect->currentIndex();
        if (currentAxis == axis) {
            updateStatus(statusMsg);
        }
    }
}
