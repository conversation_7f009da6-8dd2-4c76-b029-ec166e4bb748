#include "homewidget.h"
#include "ui_homewidget.h"
#include <QMessageBox>
#include <QTabWidget>
#include <QTimer>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QSpinBox>

HomeWidget::HomeWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::HomeWidget),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_statusWidget(nullptr),
    m_unitConverter(UnitConverter::getInstance())
{
    // 初始化回零状态和定时器
    for (int i = 0; i < 4; ++i) {
        m_isHoming[i] = false;
        m_statusTimer[i] = new QTimer(this);
        connect(m_statusTimer[i], &QTimer::timeout, [this, i]() {
            checkHomeStatus(i);
        });
        m_statusTimer[i]->setInterval(500); // 每500毫秒检查一次状态
    }

    ui->setupUi(this);

    // 初始化控件
    for (int i = 0; i < 4; ++i) {
        ui->comboAxisSelect->addItem(QString("轴 %1").arg(i));
    }

    // 连接信号槽
    connect(ui->btnStartHome, &QPushButton::clicked, this, &HomeWidget::startHome);
    connect(ui->comboAxisSelect, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &HomeWidget::onAxisChanged);

    // 创建轴参数初始化模块
    createAxisInitWidget();

    // 状态定时器已在构造函数初始化部分初始化

    // 获取轴状态模块实例
    m_statusWidget = StatusWidget::getInstance();

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &HomeWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout->addWidget(unitGroupBox);

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &HomeWidget::onUnitTypeChanged);
    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();

    // 初始化状态
    updateStatus("就绪");
}

HomeWidget::~HomeWidget()
{
    // 停止所有定时器
    for (int i = 0; i < 4; ++i) {
        if (m_statusTimer[i]->isActive()) {
            m_statusTimer[i]->stop();
        }
    }

    delete ui;
}

void HomeWidget::startHome()
{
    int axis = ui->comboAxisSelect->currentIndex();

    // 检查是否已连接
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法执行回零操作");
        emit apiStatusChanged("设备未连接，无法执行回零操作", false);
        return;
    }

    // 调用底层API执行回零操作
    short result = m_apiWrapper->axisGoHome(axis);

    if (result != 0) {
        QString errorMsg = QString("轴%1回零操作失败，错误码: %2").arg(axis).arg(result);
        QMessageBox::warning(this, "错误", errorMsg);
        emit apiStatusChanged(errorMsg, false);
        return;
    }

    QString statusMsg = QString("轴%1正在执行回零操作").arg(axis);
    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);

    // 只更新当前轴的回零状态
    m_isHoming[axis] = true;

    // 启动当前轴的状态检测定时器
    m_statusTimer[axis]->start();
}





void HomeWidget::updateStatus(const QString &message)
{
    ui->labelStatus->setText(message);
}

void HomeWidget::onApiStatusChanged(const QString& message, bool isSuccess)
{
    // 更新状态标签
    updateStatus(message);

    // 将信号转发给MainWindow
    emit apiStatusChanged(message, isSuccess);
}

void HomeWidget::onAxisChanged(int axis)
{
    // 当轴变化时，更新状态显示
    QString statusMsg;

    if (m_isHoming[axis]) {
        // 如果切换到的轴正在回零，显示其状态
        statusMsg = QString("轴%1正在执行回零操作").arg(axis);
    } else {
        // 如果切换到的轴没有回零，显示其就绪状态
        statusMsg = QString("轴%1就绪，可以开始回零操作").arg(axis);
    }

    updateStatus(statusMsg);
    emit apiStatusChanged(statusMsg, true);
}

void HomeWidget::updateUnitDisplay()
{
    // 单位显示更新逻辑（如果需要的话）
}

void HomeWidget::setDefaultParameters()
{
    // 设置默认参数逻辑（如果需要的话）
}

void HomeWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();
}

void HomeWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}

void HomeWidget::createAxisInitWidget()
{
    // 创建轴参数初始化分组框
    QGroupBox* axisInitGroupBox = new QGroupBox("轴参数初始化");
    QVBoxLayout* mainLayout = new QVBoxLayout(axisInitGroupBox);

    // 创建网格布局，确保所有元素对齐
    QGridLayout* gridLayout = new QGridLayout();

    // 设置列的最小宽度，确保对齐
    gridLayout->setColumnMinimumWidth(0, 80);  // 参数名称列
    gridLayout->setColumnMinimumWidth(1, 60);  // X轴标签列
    gridLayout->setColumnMinimumWidth(2, 120); // X轴输入框列
    gridLayout->setColumnMinimumWidth(3, 60);  // Y轴标签列
    gridLayout->setColumnMinimumWidth(4, 120); // Y轴输入框列

    int row = 0;

    // 坐标系选择 (单独一行)
    gridLayout->addWidget(new QLabel("坐标系:"), row, 0);
    QComboBox* crdComboBox = new QComboBox();
    crdComboBox->addItem("坐标系 0");
    crdComboBox->addItem("坐标系 1");
    crdComboBox->setFixedWidth(120);
    gridLayout->addWidget(crdComboBox, row, 1, 1, 2); // 跨两列
    row++;

    // 轴映射设置
    gridLayout->addWidget(new QLabel("轴映射:"), row, 0);
    gridLayout->addWidget(new QLabel("X轴:"), row, 1);
    QSpinBox* xAxisSpinBox = new QSpinBox();
    xAxisSpinBox->setRange(0, 3);
    xAxisSpinBox->setValue(0);
    xAxisSpinBox->setFixedWidth(120);
    gridLayout->addWidget(xAxisSpinBox, row, 2);

    gridLayout->addWidget(new QLabel("Y轴:"), row, 3);
    QSpinBox* yAxisSpinBox = new QSpinBox();
    yAxisSpinBox->setRange(0, 3);
    yAxisSpinBox->setValue(1);
    yAxisSpinBox->setFixedWidth(120);
    gridLayout->addWidget(yAxisSpinBox, row, 4);
    row++;

    // 轴方向设置
    gridLayout->addWidget(new QLabel("轴方向:"), row, 0);
    gridLayout->addWidget(new QLabel("X方向:"), row, 1);
    QComboBox* xDirComboBox = new QComboBox();
    xDirComboBox->addItem("0");
    xDirComboBox->addItem("1");
    xDirComboBox->setCurrentIndex(1);
    xDirComboBox->setFixedWidth(120);
    gridLayout->addWidget(xDirComboBox, row, 2);

    gridLayout->addWidget(new QLabel("Y方向:"), row, 3);
    QComboBox* yDirComboBox = new QComboBox();
    yDirComboBox->addItem("0");
    yDirComboBox->addItem("1");
    yDirComboBox->setCurrentIndex(1);
    yDirComboBox->setFixedWidth(120);
    gridLayout->addWidget(yDirComboBox, row, 4);
    row++;

    // 最大速度设置
    gridLayout->addWidget(new QLabel("最大速度:"), row, 0);
    gridLayout->addWidget(new QLabel("X轴:"), row, 1);
    QSpinBox* xVelMaxSpinBox = new QSpinBox();
    xVelMaxSpinBox->setRange(1, 100000);
    xVelMaxSpinBox->setValue(3000);
    xVelMaxSpinBox->setSuffix(" pulse/ms");
    xVelMaxSpinBox->setFixedWidth(120);
    gridLayout->addWidget(xVelMaxSpinBox, row, 2);

    gridLayout->addWidget(new QLabel("Y轴:"), row, 3);
    QSpinBox* yVelMaxSpinBox = new QSpinBox();
    yVelMaxSpinBox->setRange(1, 100000);
    yVelMaxSpinBox->setValue(3000);
    yVelMaxSpinBox->setSuffix(" pulse/ms");
    yVelMaxSpinBox->setFixedWidth(120);
    gridLayout->addWidget(yVelMaxSpinBox, row, 4);
    row++;

    // 最大加速度设置
    gridLayout->addWidget(new QLabel("最大加速度:"), row, 0);
    gridLayout->addWidget(new QLabel("X轴:"), row, 1);
    QSpinBox* xAccMaxSpinBox = new QSpinBox();
    xAccMaxSpinBox->setRange(1, 100000);
    xAccMaxSpinBox->setValue(30);
    xAccMaxSpinBox->setSuffix(" pulse/ms²");
    xAccMaxSpinBox->setFixedWidth(120);
    gridLayout->addWidget(xAccMaxSpinBox, row, 2);

    gridLayout->addWidget(new QLabel("Y轴:"), row, 3);
    QSpinBox* yAccMaxSpinBox = new QSpinBox();
    yAccMaxSpinBox->setRange(1, 100000);
    yAccMaxSpinBox->setValue(30);
    yAccMaxSpinBox->setSuffix(" pulse/ms²");
    yAccMaxSpinBox->setFixedWidth(120);
    gridLayout->addWidget(yAccMaxSpinBox, row, 4);
    row++;

    // 正向限位设置
    gridLayout->addWidget(new QLabel("正向限位:"), row, 0);
    gridLayout->addWidget(new QLabel("X轴:"), row, 1);
    QSpinBox* xPositiveSpinBox = new QSpinBox();
    xPositiveSpinBox->setRange(-1000000, 1000000);
    xPositiveSpinBox->setValue(100000);
    xPositiveSpinBox->setSuffix(" pulse");
    xPositiveSpinBox->setFixedWidth(120);
    gridLayout->addWidget(xPositiveSpinBox, row, 2);

    gridLayout->addWidget(new QLabel("Y轴:"), row, 3);
    QSpinBox* yPositiveSpinBox = new QSpinBox();
    yPositiveSpinBox->setRange(-1000000, 1000000);
    yPositiveSpinBox->setValue(100000);
    yPositiveSpinBox->setSuffix(" pulse");
    yPositiveSpinBox->setFixedWidth(120);
    gridLayout->addWidget(yPositiveSpinBox, row, 4);
    row++;

    // 负向限位设置
    gridLayout->addWidget(new QLabel("负向限位:"), row, 0);
    gridLayout->addWidget(new QLabel("X轴:"), row, 1);
    QSpinBox* xNegativeSpinBox = new QSpinBox();
    xNegativeSpinBox->setRange(-1000000, 1000000);
    xNegativeSpinBox->setValue(0);
    xNegativeSpinBox->setSuffix(" pulse");
    xNegativeSpinBox->setFixedWidth(120);
    gridLayout->addWidget(xNegativeSpinBox, row, 2);

    gridLayout->addWidget(new QLabel("Y轴:"), row, 3);
    QSpinBox* yNegativeSpinBox = new QSpinBox();
    yNegativeSpinBox->setRange(-1000000, 1000000);
    yNegativeSpinBox->setValue(0);
    yNegativeSpinBox->setSuffix(" pulse");
    yNegativeSpinBox->setFixedWidth(120);
    gridLayout->addWidget(yNegativeSpinBox, row, 4);
    row++;

    // 设置网格布局的间距
    gridLayout->setHorizontalSpacing(10);
    gridLayout->setVerticalSpacing(8);

    // 将网格布局添加到主布局
    mainLayout->addLayout(gridLayout);

    // 创建设置参数按钮
    QPushButton* setParamsButton = new QPushButton("设置参数");
    setParamsButton->setFixedHeight(30);
    setParamsButton->setStyleSheet("QPushButton { font-weight: bold; }");

    // 创建按钮布局，居中显示
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(setParamsButton);
    buttonLayout->addStretch();

    mainLayout->addLayout(buttonLayout);

    connect(setParamsButton, &QPushButton::clicked, [=]() {
        // 获取参数值
        short crd = crdComboBox->currentIndex();
        short axisMap[2] = {(short)xAxisSpinBox->value(), (short)yAxisSpinBox->value()};
        short axisDir[2] = {(short)xDirComboBox->currentIndex(), (short)yDirComboBox->currentIndex()};
        int32_t velMax[2] = {xVelMaxSpinBox->value(), yVelMaxSpinBox->value()};
        int32_t accMax[2] = {xAccMaxSpinBox->value(), yAccMaxSpinBox->value()};
        int32_t positive[2] = {xPositiveSpinBox->value(), yPositiveSpinBox->value()};
        int32_t negative[2] = {xNegativeSpinBox->value(), yNegativeSpinBox->value()};

        // 调用API设置轴参数
        short result = m_apiWrapper->setAxisPrm(crd, axisMap, axisDir, velMax, accMax, positive, negative);

        if (result == 0) {
            QString msg = QString("坐标系%1轴参数设置成功").arg(crd);
            updateStatus(msg);
            emit apiStatusChanged(msg, true);
        } else {
            QString msg = QString("坐标系%1轴参数设置失败，错误码: %2").arg(crd).arg(result);
            updateStatus(msg);
            emit apiStatusChanged(msg, false);
        }
    });

    // 将轴参数初始化模块添加到主布局
    ui->verticalLayout->insertWidget(2, axisInitGroupBox); // 插入到回零操作模块之后，状态模块之前
}

void HomeWidget::checkHomeStatus(int axis)
{
    // 只有在该轴回零过程中才检查状态
    if (!m_isHoming[axis] || !m_apiWrapper->isConnected()) {
        return;
    }

    // 从轴状态模块获取轴状态
    bool isAtOrigin = false;
    bool isResetting = false;

    if (m_statusWidget) {
        // 使用轴状态模块的方法获取状态
        isAtOrigin = m_statusWidget->isAxisAtOrigin(axis);
        isResetting = m_statusWidget->isAxisResetting(axis);
    } else {
        // 如果轴状态模块不可用，则直接获取
        short status = 0;
        short result = m_apiWrapper->getAxisStatus(axis, status);
        if (result == 0) {
            isAtOrigin = (status & (1 << 3)) != 0; // 原点信号
            isResetting = (status & (1 << 8)) != 0; // 复位中
        }
    }

    // 当到达原点且复位结束时，表示回零完成
    if (isAtOrigin && !isResetting) {
        QString statusMsg = QString("轴%1回零操作完成").arg(axis);
        updateStatus(statusMsg);
        emit apiStatusChanged(statusMsg, true);

        // 重置该轴的回零状态
        m_isHoming[axis] = false;

        // 停止该轴的定时器
        m_statusTimer[axis]->stop();

        // 如果当前显示的是该轴，更新UI状态
        int currentAxis = ui->comboAxisSelect->currentIndex();
        if (currentAxis == axis) {
            updateStatus(statusMsg);
        }
    }
}
